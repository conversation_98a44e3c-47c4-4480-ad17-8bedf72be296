import { UploadedPdf } from "@/components/UploadedPdfsTable";

/**
 * Test utility to verify latest receipt detection logic
 */

// Test function to verify latest receipt detection
export function testLatestReceiptDetection() {
  console.log('Testing Latest Receipt Detection Logic');
  console.log('=====================================');

  // Test case 1: Empty array
  const emptyReceipts: UploadedPdf[] = [];
  console.log('Test 1 - Empty array:', getLatestReceipt(emptyReceipts)); // Should be null

  // Test case 2: Single receipt
  const singleReceipt: UploadedPdf[] = [
    {
      id: '1',
      fileName: 'receipt1.pdf',
      date: new Date('2024-01-15'),
      totalCost: 25.50,
    }
  ];
  console.log('Test 2 - Single receipt:', getLatestReceipt(singleReceipt)?.fileName); // Should be receipt1.pdf

  // Test case 3: Multiple receipts with different dates
  const multipleReceipts: UploadedPdf[] = [
    {
      id: '1',
      fileName: 'receipt1.pdf',
      date: new Date('2024-01-15'),
      totalCost: 25.50,
    },
    {
      id: '2',
      fileName: 'receipt2.pdf',
      date: new Date('2024-02-20'),
      totalCost: 15.75,
    },
    {
      id: '3',
      fileName: 'receipt3.pdf',
      date: new Date('2024-01-10'),
      totalCost: 32.25,
    },
  ];
  console.log('Test 3 - Multiple receipts:', getLatestReceipt(multipleReceipts)?.fileName); // Should be receipt2.pdf

  // Test case 4: Receipts with same date
  const sameDateReceipts: UploadedPdf[] = [
    {
      id: '1',
      fileName: 'receipt1.pdf',
      date: new Date('2024-01-15'),
      totalCost: 25.50,
    },
    {
      id: '3',
      fileName: 'receipt3.pdf',
      date: new Date('2024-01-15'),
      totalCost: 32.25,
    },
    {
      id: '2',
      fileName: 'receipt2.pdf',
      date: new Date('2024-01-15'),
      totalCost: 15.75,
    },
  ];
  console.log('Test 4 - Same date receipts:', getLatestReceipt(sameDateReceipts)?.fileName); // Should be receipt3.pdf (highest id)
}

// Helper function that mimics the logic in LatestReceiptIndicator
function getLatestReceipt(receipts: UploadedPdf[]): UploadedPdf | null {
  if (!receipts || receipts.length === 0) {
    return null;
  }

  return receipts.reduce((latest, current) => {
    // Handle case where dates might be equal by also considering the id
    if (current.date > latest.date) {
      return current;
    } else if (current.date.getTime() === latest.date.getTime()) {
      // If dates are equal, prefer the one with the higher id (assuming newer uploads have higher ids)
      return current.id > latest.id ? current : latest;
    }
    return latest;
  });
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  testLatestReceiptDetection();
}
