"use client";

import React from "react";
import { UploadedPdf } from "./UploadedPdfsTable";

interface LatestReceiptIndicatorProps {
  receipts: UploadedPdf[];
}

export default function LatestReceiptIndicator({
  receipts,
}: LatestReceiptIndicatorProps) {
  // Don't show anything if no receipts exist
  if (!receipts || receipts.length === 0) {
    return null;
  }

  // Find the most recently uploaded receipt (by date)
  const latestReceipt = receipts.reduce((latest, current) => {
    // Handle case where dates might be equal by also considering the id
    if (current.date > latest.date) {
      return current;
    } else if (current.date.getTime() === latest.date.getTime()) {
      // If dates are equal, prefer the one with the higher id (assuming newer uploads have higher ids)
      return current.id > latest.id ? current : latest;
    }
    return latest;
  });

  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg
            className="h-5 w-5 text-green-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-green-800">
            Latest Receipt Uploaded
          </h3>
          <div className="mt-2 text-sm text-green-700">
            <p>
              <span className="font-medium">{latestReceipt.fileName}</span>
              {" • "}
              <span>{latestReceipt.date.toLocaleDateString()}</span>
              {" • "}
              <span className="font-medium">
                ${latestReceipt.totalCost.toFixed(2)}
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
