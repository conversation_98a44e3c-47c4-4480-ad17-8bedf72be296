"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { UploadedPdf } from "./UploadedPdfsTable";

const MONTHS_TRAVELLING_WINDOW = 12;

interface MonthlySpendingChartProps {
  receipts: UploadedPdf[];
}

interface MonthlyData {
  month: string;
  amount: number;
}

export default function MonthlySpendingChart({
  receipts,
}: MonthlySpendingChartProps) {
  // State for timeline navigation (offset in months from current date)
  const [timelineOffset, setTimelineOffset] = useState(0);

  // Generate monthly data for a 12-month window based on timeline offset
  const generateMonthlyData = (): MonthlyData[] => {
    const monthlySpending = new Map<string, number>();
    const now = new Date();

    // Initialize 12 months with 0 spending, starting from the offset
    for (let i = 11; i >= 0; i--) {
      const date = new Date(
        now.getFullYear(),
        now.getMonth() - i - timelineOffset,
        1
      );
      const monthKey = `${date.getFullYear()}-${String(
        date.getMonth() + 1
      ).padStart(2, "0")}`;
      monthlySpending.set(monthKey, 0);
    }

    // Aggregate spending by month from receipts
    receipts.forEach((receipt) => {
      const receiptDate = new Date(receipt.date);
      const monthKey = `${receiptDate.getFullYear()}-${String(
        receiptDate.getMonth() + 1
      ).padStart(2, "0")}`;

      if (monthlySpending.has(monthKey)) {
        monthlySpending.set(
          monthKey,
          monthlySpending.get(monthKey)! + receipt.totalCost
        );
      }
    });

    // Convert to chart data format
    return Array.from(monthlySpending.entries()).map(([monthKey, amount]) => {
      const [year, month] = monthKey.split("-");
      const date = new Date(parseInt(year), parseInt(month) - 1, 1);
      const monthLabel = date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
      });

      return {
        month: monthLabel,
        amount: Math.round(amount * 100) / 100, // Round to 2 decimal places
      };
    });
  };

  const chartData = generateMonthlyData();

  // Navigation functions
  const goBack = () => {
    setTimelineOffset(timelineOffset + MONTHS_TRAVELLING_WINDOW); // Go back MONTHS_TRAVELLING_WINDOW months
  };

  const goForward = () => {
    setTimelineOffset(Math.max(0, timelineOffset - MONTHS_TRAVELLING_WINDOW)); // Go forward MONTHS_TRAVELLING_WINDOW months, but not beyond current
  };

  // Helper function to get the date range for display
  const getDateRange = () => {
    const now = new Date();
    const endDate = new Date(
      now.getFullYear(),
      now.getMonth() - timelineOffset,
      1
    );
    const startDate = new Date(
      now.getFullYear(),
      now.getMonth() - 11 - timelineOffset,
      1
    );

    const formatDate = (date: Date) =>
      date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
      });

    return `${formatDate(startDate)} - ${formatDate(endDate)}`;
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-lg font-medium text-gray-900">
            Monthly Spending
          </h2>
          <p className="text-sm text-gray-500">{getDateRange()}</p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={goBack}
            className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            title="Go back 6 months"
          >
            <svg
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <button
            onClick={goForward}
            disabled={timelineOffset === 0}
            className="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
            title="Go forward 6 months"
          >
            <svg
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
      </div>
      <div className="h-80">
        {chartData.some((data) => data.amount > 0) ? (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="month"
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `$${value}`}
              />
              <Tooltip
                formatter={(value: number) => [
                  `$${value.toFixed(2)}`,
                  "Amount",
                ]}
                labelStyle={{ color: "#374151" }}
                contentStyle={{
                  backgroundColor: "#f9fafb",
                  border: "1px solid #d1d5db",
                  borderRadius: "6px",
                }}
              />
              <Bar dataKey="amount" fill="#4f46e5" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        ) : (
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No spending data
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Upload some receipts to see your monthly spending trends.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
